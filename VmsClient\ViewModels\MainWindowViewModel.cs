using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using VmsClient.Models;
using VmsClient.Services.Communication;
using VmsClient.Services.Configuration;
using VmsClient.Services.Discovery;

namespace VmsClient.ViewModels;

/// <summary>
/// Main window view model
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly INetworkDiscoveryService _discoveryService;
    private readonly IVmsApiClient _apiClient;
    private readonly IConfigurationService _configService;

    [ObservableProperty]
    private string _title = "VMS Client - Voucher Management System";

    [ObservableProperty]
    private bool _isDiscovering = false;

    [ObservableProperty]
    private bool _isConnected = false;

    [ObservableProperty]
    private VmsServer? _selectedServer;

    [ObservableProperty]
    private string _connectionStatus = "Disconnected";

    [ObservableProperty]
    private string _discoveryStatus = "Ready";

    [ObservableProperty]
    private int _discoveredServerCount = 0;

    public ObservableCollection<VmsServer> DiscoveredServers { get; } = new();

    public MainWindowViewModel(
        ILogger<MainWindowViewModel> logger,
        INetworkDiscoveryService discoveryService,
        IVmsApiClient apiClient,
        IConfigurationService configService)
    {
        _logger = logger;
        _discoveryService = discoveryService;
        _apiClient = apiClient;
        _configService = configService;

        // Subscribe to events
        _discoveryService.ServerDiscovered += OnServerDiscovered;
        _discoveryService.ServerLost += OnServerLost;
        _discoveryService.DiscoveryProgress += OnDiscoveryProgress;
        _apiClient.ConnectionStatusChanged += OnConnectionStatusChanged;

        // Load saved servers
        LoadSavedServers();
    }

    [RelayCommand]
    private async Task StartDiscoveryAsync()
    {
        try
        {
            _logger.LogInformation("Starting server discovery");
            await _discoveryService.StartDiscoveryAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start discovery");
            DiscoveryStatus = $"Discovery failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task StopDiscoveryAsync()
    {
        try
        {
            _logger.LogInformation("Stopping server discovery");
            await _discoveryService.StopDiscoveryAsync();
            DiscoveryStatus = "Discovery stopped";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop discovery");
        }
    }

    [RelayCommand]
    private async Task ConnectToServerAsync()
    {
        if (SelectedServer == null) return;

        try
        {
            _logger.LogInformation("Connecting to server {ServerUrl}", SelectedServer.Url);
            ConnectionStatus = "Connecting...";
            
            var connected = await _apiClient.ConnectAsync(SelectedServer);
            if (connected)
            {
                ConnectionStatus = $"Connected to {SelectedServer.DisplayName}";
                
                // Try to authenticate if we have saved credentials
                var connectionSettings = _configService.GetConnectionSettings(SelectedServer.Id);
                if (connectionSettings?.RememberCredentials == true && 
                    !string.IsNullOrEmpty(connectionSettings.LastUsername) &&
                    !string.IsNullOrEmpty(connectionSettings.EncryptedPassword))
                {
                    await _apiClient.AuthenticateAsync(
                        connectionSettings.LastUsername, 
                        connectionSettings.EncryptedPassword);
                }
            }
            else
            {
                ConnectionStatus = "Connection failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to server");
            ConnectionStatus = $"Connection error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task DisconnectFromServerAsync()
    {
        try
        {
            _logger.LogInformation("Disconnecting from server");
            await _apiClient.DisconnectAsync();
            ConnectionStatus = "Disconnected";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to disconnect from server");
        }
    }

    [RelayCommand]
    private async Task RefreshServersAsync()
    {
        try
        {
            _logger.LogInformation("Refreshing server list");
            DiscoveryStatus = "Refreshing...";
            
            var servers = await _discoveryService.DiscoverServersAsync();
            DiscoveryStatus = $"Found {servers.Count} servers";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh servers");
            DiscoveryStatus = $"Refresh failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task AddServerManuallyAsync(string? ipAddress)
    {
        if (string.IsNullOrWhiteSpace(ipAddress)) return;

        try
        {
            _logger.LogInformation("Adding server manually: {IpAddress}", ipAddress);
            await _discoveryService.AddServerManuallyAsync(ipAddress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add server manually");
            DiscoveryStatus = $"Failed to add server: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task SaveCurrentServerAsync()
    {
        if (SelectedServer == null) return;

        try
        {
            await _configService.AddOrUpdateServerAsync(SelectedServer);
            _logger.LogInformation("Server saved: {ServerName}", SelectedServer.DisplayName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save server");
        }
    }

    private void OnServerDiscovered(object? sender, VmsServer server)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            if (!DiscoveredServers.Any(s => s.Equals(server)))
            {
                DiscoveredServers.Add(server);
                DiscoveredServerCount = DiscoveredServers.Count;
                _logger.LogInformation("Server discovered: {ServerName}", server.DisplayName);
            }
        });
    }

    private void OnServerLost(object? sender, VmsServer server)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var existingServer = DiscoveredServers.FirstOrDefault(s => s.Equals(server));
            if (existingServer != null)
            {
                DiscoveredServers.Remove(existingServer);
                DiscoveredServerCount = DiscoveredServers.Count;
                _logger.LogInformation("Server lost: {ServerName}", server.DisplayName);
            }
        });
    }

    private void OnDiscoveryProgress(object? sender, DiscoveryProgressEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsDiscovering = _discoveryService.IsDiscovering;
            DiscoveryStatus = e.CurrentOperation;
            
            if (e.TotalHosts > 0)
            {
                var percentage = (int)e.ProgressPercentage;
                DiscoveryStatus = $"{e.CurrentOperation} ({percentage}%)";
            }
        });
    }

    private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsConnected = e.IsConnected;
            
            if (e.IsConnected && e.Server != null)
            {
                ConnectionStatus = $"Connected to {e.Server.DisplayName}";
            }
            else if (!string.IsNullOrEmpty(e.ErrorMessage))
            {
                ConnectionStatus = $"Connection failed: {e.ErrorMessage}";
            }
            else
            {
                ConnectionStatus = "Disconnected";
            }
        });
    }

    private void LoadSavedServers()
    {
        try
        {
            var savedServers = _configService.GetSavedServers();
            foreach (var server in savedServers)
            {
                if (!DiscoveredServers.Any(s => s.Equals(server)))
                {
                    DiscoveredServers.Add(server);
                }
            }
            DiscoveredServerCount = DiscoveredServers.Count;
            
            _logger.LogInformation("Loaded {Count} saved servers", savedServers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load saved servers");
        }
    }
}
