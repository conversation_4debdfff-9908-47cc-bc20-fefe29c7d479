﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Windows;
using VmsClient.Services.Communication;
using VmsClient.Services.Configuration;
using VmsClient.Services.Discovery;
using VmsClient.ViewModels;

namespace VmsClient;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // Build the host
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Register services
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                services.AddSingleton<INetworkDiscoveryService, NetworkDiscoveryService>();
                services.AddSingleton<IVmsApiClient, VmsApiClient>();

                // Register ViewModels
                services.AddTransient<MainWindowViewModel>();
                services.AddTransient<ServerDiscoveryViewModel>();
                services.AddTransient<VoucherManagementViewModel>();

                // Register Views
                services.AddTransient<MainWindow>();

                // Configure logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.AddDebug();
                    builder.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Information);
                });
            })
            .Build();

        // Start the host
        await _host.StartAsync();

        // Show main window
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        base.OnExit(e);
    }
}

