# VMS Client - Voucher Management System

A modern .NET 9 WPF application for connecting to and managing VMS (Voucher Management System) servers with automatic network discovery capabilities.

## 🚀 Features

### ✅ Implemented
- **Automatic Network Discovery**: Scans local networks to find VMS servers automatically
- **Manual Server Addition**: Add servers manually by IP address and port
- **Server Connection Management**: Connect/disconnect from VMS servers with status monitoring
- **Configuration Persistence**: Saves discovered servers and connection settings
- **Modern WPF UI**: Clean, responsive user interface with tabbed navigation
- **Real-time Status Updates**: Live connection status and discovery progress
- **Secure Credential Storage**: Encrypted password storage using Windows Data Protection API

### 🔄 In Development
- **Voucher Management**: Full CRUD operations for vouchers
- **Advanced Settings**: Comprehensive configuration options
- **Connection Resilience**: Automatic reconnection and failover capabilities
- **Offline Mode**: Limited functionality when disconnected

## 🏗️ Architecture

The application follows a clean, layered architecture:

```
├── Presentation Layer (WPF Views & ViewModels)
├── Application Layer (Services & Commands)
├── Domain Layer (Business Logic)
└── Infrastructure Layer (Network, HTTP, Configuration)
```

### Key Components

- **NetworkDiscoveryService**: Automatically discovers VMS servers on the network
- **VmsApiClient**: HTTP client for communicating with VMS servers
- **ConfigurationService**: Manages application settings and server configurations
- **MVVM Pattern**: Uses CommunityToolkit.Mvvm for clean separation of concerns

## 🔧 Technical Stack

- **.NET 9**: Latest .NET framework
- **WPF**: Windows Presentation Foundation for UI
- **CommunityToolkit.Mvvm**: Modern MVVM framework
- **Microsoft.Extensions.Hosting**: Dependency injection and hosting
- **System.Text.Json**: JSON serialization
- **System.Net.Http**: HTTP client communications

## 🚦 Getting Started

### Prerequisites
- Windows 10/11
- .NET 9 SDK

### Building and Running

1. **Clone or extract the project**
2. **Build the application**:
   ```bash
   dotnet build VmsClient
   ```
3. **Run the application**:
   ```bash
   dotnet run --project VmsClient
   ```

### Testing VMS Server Connection

A simple test utility is included to verify connectivity:

```bash
dotnet run --project VmsClientTest
```

## 🔍 Network Discovery

The application uses multiple discovery methods:

1. **Network Scanning**: Scans local network ranges for open ports
2. **HTTP Endpoint Testing**: Tests multiple common VMS endpoints
3. **Manual Addition**: Allows manual server specification
4. **Saved Servers**: Remembers previously discovered servers

### Supported Network Ranges
- Private networks (10.x.x.x, 172.16-31.x.x, 192.168.x.x)
- Automatic subnet detection based on network interfaces
- Configurable port scanning (default: 8080)

## 📁 Configuration

Configuration files are stored in:
```
%APPDATA%\VmsClient\
├── appsettings.json      # Application settings
├── servers.json          # Discovered servers
├── connections.json      # Connection settings (encrypted passwords)
└── preferences.json      # User preferences
```

## 🔒 Security

- **Encrypted Passwords**: Uses Windows DPAPI for secure credential storage
- **HTTPS Support**: Ready for secure connections (when VMS server supports it)
- **No Hardcoded IPs**: Dynamic discovery eliminates hardcoded server addresses

## 🎯 Tested Compatibility

✅ **Confirmed Working**: VMS Server at `************:8080`
- Successfully connects and communicates
- Responds to HTTP requests
- Compatible with the discovery system

## 🔮 Future Enhancements

- **Multi-server Management**: Connect to multiple VMS servers simultaneously
- **Advanced Filtering**: Filter vouchers by various criteria
- **Export/Import**: Data export and import capabilities
- **Notifications**: System tray notifications for important events
- **Themes**: Light/dark theme support
- **Localization**: Multi-language support

## 🐛 Troubleshooting

### Connection Issues
1. Verify VMS server is running and accessible
2. Check firewall settings
3. Ensure correct IP address and port
4. Try manual server addition if discovery fails

### Discovery Issues
1. Check network connectivity
2. Verify network permissions
3. Try different network interfaces
4. Use manual server addition as fallback

## 📝 License

This project is part of the Payroll Auditor system.

---

**Built with ❤️ using .NET 9 and modern development practices**
