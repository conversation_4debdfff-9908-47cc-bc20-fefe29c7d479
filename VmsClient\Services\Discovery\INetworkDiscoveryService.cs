using VmsClient.Models;

namespace VmsClient.Services.Discovery;

/// <summary>
/// Service for discovering VMS servers on the network
/// </summary>
public interface INetworkDiscoveryService
{
    /// <summary>
    /// Event raised when a new server is discovered
    /// </summary>
    event EventHandler<VmsServer> ServerDiscovered;
    
    /// <summary>
    /// Event raised when a server goes offline
    /// </summary>
    event EventHandler<VmsServer> ServerLost;
    
    /// <summary>
    /// Event raised when discovery progress changes
    /// </summary>
    event EventHandler<DiscoveryProgressEventArgs> DiscoveryProgress;
    
    /// <summary>
    /// Gets all currently discovered servers
    /// </summary>
    IReadOnlyList<VmsServer> DiscoveredServers { get; }
    
    /// <summary>
    /// Indicates if discovery is currently running
    /// </summary>
    bool IsDiscovering { get; }
    
    /// <summary>
    /// Start automatic network discovery
    /// </summary>
    Task StartDiscoveryAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stop automatic network discovery
    /// </summary>
    Task StopDiscoveryAsync();
    
    /// <summary>
    /// Perform a one-time discovery scan
    /// </summary>
    Task<IList<VmsServer>> DiscoverServersAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Test connection to a specific server
    /// </summary>
    Task<bool> TestServerConnectionAsync(VmsServer server, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Add a server manually
    /// </summary>
    Task AddServerManuallyAsync(string ipAddress, int port = 8080);
}

/// <summary>
/// Event arguments for discovery progress
/// </summary>
public class DiscoveryProgressEventArgs : EventArgs
{
    public int TotalHosts { get; set; }
    public int ScannedHosts { get; set; }
    public int FoundServers { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
    public double ProgressPercentage => TotalHosts > 0 ? (double)ScannedHosts / TotalHosts * 100 : 0;
}
