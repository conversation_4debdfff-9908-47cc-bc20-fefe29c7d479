using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using VmsClient.Models;
using VmsClient.Services.Discovery;

namespace VmsClient.ViewModels;

/// <summary>
/// Server discovery view model
/// </summary>
public partial class ServerDiscoveryViewModel : ObservableObject
{
    private readonly ILogger<ServerDiscoveryViewModel> _logger;
    private readonly INetworkDiscoveryService _discoveryService;

    [ObservableProperty]
    private bool _isDiscovering = false;

    [ObservableProperty]
    private string _discoveryStatus = "Ready to discover servers";

    [ObservableProperty]
    private int _totalHosts = 0;

    [ObservableProperty]
    private int _scannedHosts = 0;

    [ObservableProperty]
    private int _foundServers = 0;

    [ObservableProperty]
    private double _progressPercentage = 0;

    [ObservableProperty]
    private string _manualServerIp = string.Empty;

    [ObservableProperty]
    private int _manualServerPort = 8080;

    [ObservableProperty]
    private VmsServer? _selectedServer;

    public ObservableCollection<VmsServer> DiscoveredServers { get; } = new();

    public ServerDiscoveryViewModel(
        ILogger<ServerDiscoveryViewModel> logger,
        INetworkDiscoveryService discoveryService)
    {
        _logger = logger;
        _discoveryService = discoveryService;

        // Subscribe to discovery events
        _discoveryService.ServerDiscovered += OnServerDiscovered;
        _discoveryService.ServerLost += OnServerLost;
        _discoveryService.DiscoveryProgress += OnDiscoveryProgress;

        // Load existing discovered servers
        LoadDiscoveredServers();
    }

    [RelayCommand]
    private async Task StartDiscoveryAsync()
    {
        try
        {
            _logger.LogInformation("Starting network discovery");
            await _discoveryService.StartDiscoveryAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start discovery");
            DiscoveryStatus = $"Discovery failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task StopDiscoveryAsync()
    {
        try
        {
            _logger.LogInformation("Stopping network discovery");
            await _discoveryService.StopDiscoveryAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop discovery");
        }
    }

    [RelayCommand]
    private async Task RefreshDiscoveryAsync()
    {
        try
        {
            _logger.LogInformation("Refreshing server discovery");
            var servers = await _discoveryService.DiscoverServersAsync();
            _logger.LogInformation("Discovery refresh completed, found {Count} servers", servers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh discovery");
            DiscoveryStatus = $"Refresh failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task AddServerManuallyAsync()
    {
        if (string.IsNullOrWhiteSpace(ManualServerIp))
        {
            DiscoveryStatus = "Please enter a valid IP address";
            return;
        }

        try
        {
            _logger.LogInformation("Adding server manually: {IpAddress}:{Port}", ManualServerIp, ManualServerPort);
            await _discoveryService.AddServerManuallyAsync(ManualServerIp, ManualServerPort);
            
            // Clear the input fields
            ManualServerIp = string.Empty;
            ManualServerPort = 8080;
            
            DiscoveryStatus = "Server added successfully";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to add server manually");
            DiscoveryStatus = $"Failed to add server: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task TestServerConnectionAsync(VmsServer? server)
    {
        if (server == null) return;

        try
        {
            _logger.LogInformation("Testing connection to server {ServerUrl}", server.Url);
            DiscoveryStatus = $"Testing connection to {server.DisplayName}...";
            
            var isOnline = await _discoveryService.TestServerConnectionAsync(server);
            
            if (isOnline)
            {
                DiscoveryStatus = $"Server {server.DisplayName} is online (Response: {server.ResponseTimeMs}ms)";
                server.IsOnline = true;
            }
            else
            {
                DiscoveryStatus = $"Server {server.DisplayName} is offline or unreachable";
                server.IsOnline = false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to test server connection");
            DiscoveryStatus = $"Connection test failed: {ex.Message}";
        }
    }

    [RelayCommand]
    private void RemoveServer(VmsServer? server)
    {
        if (server == null) return;

        try
        {
            DiscoveredServers.Remove(server);
            FoundServers = DiscoveredServers.Count;
            
            _logger.LogInformation("Removed server {ServerName} from list", server.DisplayName);
            DiscoveryStatus = $"Removed server {server.DisplayName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to remove server");
        }
    }

    [RelayCommand]
    private void ClearAllServers()
    {
        try
        {
            var count = DiscoveredServers.Count;
            DiscoveredServers.Clear();
            FoundServers = 0;
            
            _logger.LogInformation("Cleared all {Count} servers from list", count);
            DiscoveryStatus = $"Cleared {count} servers from list";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear servers");
        }
    }

    private void OnServerDiscovered(object? sender, VmsServer server)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            if (!DiscoveredServers.Any(s => s.Equals(server)))
            {
                DiscoveredServers.Add(server);
                FoundServers = DiscoveredServers.Count;
                
                _logger.LogInformation("Server discovered: {ServerName} at {ServerUrl}", 
                    server.DisplayName, server.Url);
            }
        });
    }

    private void OnServerLost(object? sender, VmsServer server)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var existingServer = DiscoveredServers.FirstOrDefault(s => s.Equals(server));
            if (existingServer != null)
            {
                existingServer.IsOnline = false;
                _logger.LogInformation("Server lost: {ServerName}", server.DisplayName);
            }
        });
    }

    private void OnDiscoveryProgress(object? sender, DiscoveryProgressEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsDiscovering = _discoveryService.IsDiscovering;
            TotalHosts = e.TotalHosts;
            ScannedHosts = e.ScannedHosts;
            FoundServers = e.FoundServers;
            ProgressPercentage = e.ProgressPercentage;
            DiscoveryStatus = e.CurrentOperation;
            
            if (e.TotalHosts > 0)
            {
                var percentage = (int)e.ProgressPercentage;
                DiscoveryStatus = $"{e.CurrentOperation} - {e.ScannedHosts}/{e.TotalHosts} hosts ({percentage}%)";
            }
        });
    }

    private void LoadDiscoveredServers()
    {
        try
        {
            var existingServers = _discoveryService.DiscoveredServers;
            foreach (var server in existingServers)
            {
                DiscoveredServers.Add(server);
            }
            
            FoundServers = DiscoveredServers.Count;
            _logger.LogInformation("Loaded {Count} existing discovered servers", existingServers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load discovered servers");
        }
    }
}
