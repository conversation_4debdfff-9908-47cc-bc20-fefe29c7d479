using System.Net.Http;

Console.WriteLine("🚀 VMS Client Connection Test");
Console.WriteLine("==============================");

await TestVmsServerConnection();

static async Task TestVmsServerConnection()
{
    try
    {
        Console.WriteLine("Testing connection to VMS server at ************:8080...");

        using var httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(10) };
        var baseUrl = "http://************:8080";

        // Try multiple endpoints
        var endpoints = new[] { "/", "/api/health", "/health", "/api/status", "/api/vouchers" };

        foreach (var endpoint in endpoints)
        {
            try
            {
                Console.WriteLine($"  Trying endpoint: {baseUrl}{endpoint}");
                var response = await httpClient.GetAsync($"{baseUrl}{endpoint}");

                Console.WriteLine($"  Response: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"  Content length: {content.Length} characters");

                    if (content.Length > 0 && content.Length < 500)
                    {
                        Console.WriteLine($"  Content preview: {content.Substring(0, Math.Min(200, content.Length))}");
                    }

                    Console.WriteLine($"✅ Successfully connected to VMS server at {baseUrl}{endpoint}");
                    Console.WriteLine($"   Server appears to be online and responding");
                    return;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"  HTTP error: {ex.Message}");
            }
            catch (TaskCanceledException)
            {
                Console.WriteLine($"  Timeout connecting to {baseUrl}{endpoint}");
            }
        }

        Console.WriteLine($"❌ Could not connect to VMS server at {baseUrl}");
        Console.WriteLine("   This could mean:");
        Console.WriteLine("   - Server is not running");
        Console.WriteLine("   - Server is on a different IP/port");
        Console.WriteLine("   - Network connectivity issues");
        Console.WriteLine("   - Firewall blocking the connection");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Error testing VMS server: {ex.Message}");
    }

    Console.WriteLine("\nPress any key to continue...");
    Console.ReadKey();
}
