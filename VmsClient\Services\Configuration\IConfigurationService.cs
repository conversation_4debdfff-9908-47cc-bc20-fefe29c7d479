using VmsClient.Models;

namespace VmsClient.Services.Configuration;

/// <summary>
/// Service for managing application configuration and settings
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// Event raised when configuration changes
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
    
    /// <summary>
    /// Get application settings
    /// </summary>
    AppSettings GetAppSettings();
    
    /// <summary>
    /// Save application settings
    /// </summary>
    Task SaveAppSettingsAsync(AppSettings settings);
    
    /// <summary>
    /// Get saved servers
    /// </summary>
    List<VmsServer> GetSavedServers();
    
    /// <summary>
    /// Save servers list
    /// </summary>
    Task SaveServersAsync(List<VmsServer> servers);
    
    /// <summary>
    /// Add or update a server
    /// </summary>
    Task AddOrUpdateServerAsync(VmsServer server);
    
    /// <summary>
    /// Remove a server
    /// </summary>
    Task RemoveServerAsync(string serverId);
    
    /// <summary>
    /// Get connection settings for a server
    /// </summary>
    ConnectionSettings? GetConnectionSettings(string serverId);
    
    /// <summary>
    /// Save connection settings for a server
    /// </summary>
    Task SaveConnectionSettingsAsync(string serverId, ConnectionSettings settings);
    
    /// <summary>
    /// Get user preferences
    /// </summary>
    UserPreferences GetUserPreferences();
    
    /// <summary>
    /// Save user preferences
    /// </summary>
    Task SaveUserPreferencesAsync(UserPreferences preferences);
    
    /// <summary>
    /// Reset all settings to defaults
    /// </summary>
    Task ResetToDefaultsAsync();
}

/// <summary>
/// Event arguments for configuration changes
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    public string Section { get; set; } = string.Empty;
    public object? OldValue { get; set; }
    public object? NewValue { get; set; }
}

/// <summary>
/// Application settings
/// </summary>
public class AppSettings
{
    public string Version { get; set; } = "1.0.0";
    public bool AutoStartDiscovery { get; set; } = true;
    public int DiscoveryIntervalSeconds { get; set; } = 30;
    public int ConnectionTimeoutSeconds { get; set; } = 30;
    public bool SaveCredentials { get; set; } = false;
    public bool AutoReconnect { get; set; } = true;
    public LogLevel LogLevel { get; set; } = LogLevel.Information;
    public string Theme { get; set; } = "Light";
    public string Language { get; set; } = "en-US";
    public WindowSettings MainWindow { get; set; } = new();
}

/// <summary>
/// Window settings
/// </summary>
public class WindowSettings
{
    public double Width { get; set; } = 1200;
    public double Height { get; set; } = 800;
    public double Left { get; set; } = 100;
    public double Top { get; set; } = 100;
    public bool IsMaximized { get; set; } = false;
}

/// <summary>
/// Connection settings for a specific server
/// </summary>
public class ConnectionSettings
{
    public string ServerId { get; set; } = string.Empty;
    public string? LastUsername { get; set; }
    public string? EncryptedPassword { get; set; }
    public bool RememberCredentials { get; set; } = false;
    public DateTime? LastConnected { get; set; }
    public int ConnectionAttempts { get; set; } = 0;
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// User preferences
/// </summary>
public class UserPreferences
{
    public bool ShowNotifications { get; set; } = true;
    public bool MinimizeToTray { get; set; } = false;
    public bool StartMinimized { get; set; } = false;
    public bool CheckForUpdates { get; set; } = true;
    public string DefaultCurrency { get; set; } = "USD";
    public int RefreshIntervalSeconds { get; set; } = 60;
    public bool ConfirmDeletions { get; set; } = true;
    public bool ShowAdvancedOptions { get; set; } = false;
    public List<string> RecentServers { get; set; } = new();
    public Dictionary<string, object> CustomPreferences { get; set; } = new();
}

/// <summary>
/// Log level enumeration
/// </summary>
public enum LogLevel
{
    Trace,
    Debug,
    Information,
    Warning,
    Error,
    Critical
}
