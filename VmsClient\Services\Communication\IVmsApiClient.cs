using VmsClient.Models;

namespace VmsClient.Services.Communication;

/// <summary>
/// Interface for communicating with VMS servers
/// </summary>
public interface IVmsApiClient
{
    /// <summary>
    /// Currently connected server
    /// </summary>
    VmsServer? CurrentServer { get; }
    
    /// <summary>
    /// Indicates if client is connected to a server
    /// </summary>
    bool IsConnected { get; }
    
    /// <summary>
    /// Event raised when connection status changes
    /// </summary>
    event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
    
    /// <summary>
    /// Connect to a VMS server
    /// </summary>
    Task<bool> ConnectAsync(VmsServer server, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Disconnect from current server
    /// </summary>
    Task DisconnectAsync();
    
    /// <summary>
    /// Authenticate with the server
    /// </summary>
    Task<bool> AuthenticateAsync(string username, string password, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Test server health/connectivity
    /// </summary>
    Task<ServerHealthResponse> GetServerHealthAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get server information
    /// </summary>
    Task<ServerInfoResponse> GetServerInfoAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get all vouchers
    /// </summary>
    Task<List<Voucher>> GetVouchersAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Create a new voucher
    /// </summary>
    Task<Voucher> CreateVoucherAsync(CreateVoucherRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Update an existing voucher
    /// </summary>
    Task<Voucher> UpdateVoucherAsync(string voucherId, UpdateVoucherRequest request, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Delete a voucher
    /// </summary>
    Task<bool> DeleteVoucherAsync(string voucherId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Get voucher by ID
    /// </summary>
    Task<Voucher?> GetVoucherAsync(string voucherId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Event arguments for connection status changes
/// </summary>
public class ConnectionStatusChangedEventArgs : EventArgs
{
    public bool IsConnected { get; set; }
    public VmsServer? Server { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// Server health response
/// </summary>
public class ServerHealthResponse
{
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// Server information response
/// </summary>
public class ServerInfoResponse
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> SupportedFeatures { get; set; } = new();
    public DateTime StartTime { get; set; }
}

/// <summary>
/// Voucher model
/// </summary>
public class Voucher
{
    public string Id { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public VoucherStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? UsedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string? UsedBy { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Voucher status enumeration
/// </summary>
public enum VoucherStatus
{
    Active,
    Used,
    Expired,
    Cancelled
}

/// <summary>
/// Create voucher request
/// </summary>
public class CreateVoucherRequest
{
    public decimal Amount { get; set; }
    public string Currency { get; set; } = "USD";
    public DateTime? ExpiresAt { get; set; }
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Update voucher request
/// </summary>
public class UpdateVoucherRequest
{
    public VoucherStatus? Status { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}
