using Microsoft.Extensions.Logging;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using VmsClient.Models;

namespace VmsClient.Services.Configuration;

/// <summary>
/// Configuration service that manages app settings, server configurations, and user preferences
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configDirectory;
    private readonly string _appSettingsFile;
    private readonly string _serversFile;
    private readonly string _connectionSettingsFile;
    private readonly string _userPreferencesFile;
    private readonly JsonSerializerOptions _jsonOptions;
    
    private AppSettings _appSettings;
    private List<VmsServer> _savedServers;
    private Dictionary<string, ConnectionSettings> _connectionSettings;
    private UserPreferences _userPreferences;

    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger;
        
        // Set up configuration directory
        _configDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "VmsClient");
        
        _appSettingsFile = Path.Combine(_configDirectory, "appsettings.json");
        _serversFile = Path.Combine(_configDirectory, "servers.json");
        _connectionSettingsFile = Path.Combine(_configDirectory, "connections.json");
        _userPreferencesFile = Path.Combine(_configDirectory, "preferences.json");
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
            PropertyNameCaseInsensitive = true
        };
        
        // Initialize with defaults
        _appSettings = new AppSettings();
        _savedServers = new List<VmsServer>();
        _connectionSettings = new Dictionary<string, ConnectionSettings>();
        _userPreferences = new UserPreferences();
        
        // Load existing configuration
        LoadConfiguration();
    }

    public AppSettings GetAppSettings() => _appSettings;

    public async Task SaveAppSettingsAsync(AppSettings settings)
    {
        var oldSettings = _appSettings;
        _appSettings = settings;
        
        await SaveToFileAsync(_appSettingsFile, settings);
        OnConfigurationChanged("AppSettings", oldSettings, settings);
        
        _logger.LogInformation("Application settings saved");
    }

    public List<VmsServer> GetSavedServers() => new(_savedServers);

    public async Task SaveServersAsync(List<VmsServer> servers)
    {
        var oldServers = _savedServers;
        _savedServers = new List<VmsServer>(servers);
        
        await SaveToFileAsync(_serversFile, servers);
        OnConfigurationChanged("Servers", oldServers, servers);
        
        _logger.LogInformation("Saved {Count} servers", servers.Count);
    }

    public async Task AddOrUpdateServerAsync(VmsServer server)
    {
        var existingIndex = _savedServers.FindIndex(s => s.Id == server.Id);
        if (existingIndex >= 0)
        {
            _savedServers[existingIndex] = server;
            _logger.LogInformation("Updated server {ServerName}", server.DisplayName);
        }
        else
        {
            _savedServers.Add(server);
            _logger.LogInformation("Added new server {ServerName}", server.DisplayName);
        }
        
        await SaveServersAsync(_savedServers);
    }

    public async Task RemoveServerAsync(string serverId)
    {
        var server = _savedServers.FirstOrDefault(s => s.Id == serverId);
        if (server != null)
        {
            _savedServers.Remove(server);
            
            // Also remove connection settings
            _connectionSettings.Remove(serverId);
            await SaveToFileAsync(_connectionSettingsFile, _connectionSettings);
            
            await SaveServersAsync(_savedServers);
            _logger.LogInformation("Removed server {ServerName}", server.DisplayName);
        }
    }

    public ConnectionSettings? GetConnectionSettings(string serverId)
    {
        _connectionSettings.TryGetValue(serverId, out var settings);
        return settings;
    }

    public async Task SaveConnectionSettingsAsync(string serverId, ConnectionSettings settings)
    {
        // Encrypt password if provided
        if (!string.IsNullOrEmpty(settings.EncryptedPassword))
        {
            settings.EncryptedPassword = EncryptPassword(settings.EncryptedPassword);
        }
        
        _connectionSettings[serverId] = settings;
        await SaveToFileAsync(_connectionSettingsFile, _connectionSettings);
        
        _logger.LogInformation("Saved connection settings for server {ServerId}", serverId);
    }

    public UserPreferences GetUserPreferences() => _userPreferences;

    public async Task SaveUserPreferencesAsync(UserPreferences preferences)
    {
        var oldPreferences = _userPreferences;
        _userPreferences = preferences;
        
        await SaveToFileAsync(_userPreferencesFile, preferences);
        OnConfigurationChanged("UserPreferences", oldPreferences, preferences);
        
        _logger.LogInformation("User preferences saved");
    }

    public async Task ResetToDefaultsAsync()
    {
        _logger.LogInformation("Resetting configuration to defaults");
        
        _appSettings = new AppSettings();
        _userPreferences = new UserPreferences();
        
        await SaveAppSettingsAsync(_appSettings);
        await SaveUserPreferencesAsync(_userPreferences);
        
        // Don't reset servers and connection settings as they contain user data
    }

    private void LoadConfiguration()
    {
        try
        {
            // Ensure config directory exists
            Directory.CreateDirectory(_configDirectory);
            
            // Load app settings
            if (File.Exists(_appSettingsFile))
            {
                _appSettings = LoadFromFile<AppSettings>(_appSettingsFile) ?? new AppSettings();
            }
            
            // Load servers
            if (File.Exists(_serversFile))
            {
                _savedServers = LoadFromFile<List<VmsServer>>(_serversFile) ?? new List<VmsServer>();
            }
            
            // Load connection settings
            if (File.Exists(_connectionSettingsFile))
            {
                _connectionSettings = LoadFromFile<Dictionary<string, ConnectionSettings>>(_connectionSettingsFile) 
                    ?? new Dictionary<string, ConnectionSettings>();
                
                // Decrypt passwords
                foreach (var kvp in _connectionSettings)
                {
                    if (!string.IsNullOrEmpty(kvp.Value.EncryptedPassword))
                    {
                        try
                        {
                            kvp.Value.EncryptedPassword = DecryptPassword(kvp.Value.EncryptedPassword);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to decrypt password for server {ServerId}", kvp.Key);
                            kvp.Value.EncryptedPassword = null;
                        }
                    }
                }
            }
            
            // Load user preferences
            if (File.Exists(_userPreferencesFile))
            {
                _userPreferences = LoadFromFile<UserPreferences>(_userPreferencesFile) ?? new UserPreferences();
            }
            
            _logger.LogInformation("Configuration loaded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load configuration");
        }
    }

    private T? LoadFromFile<T>(string filePath) where T : class
    {
        try
        {
            var json = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<T>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load configuration from {FilePath}", filePath);
            return null;
        }
    }

    private async Task SaveToFileAsync<T>(string filePath, T data)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save configuration to {FilePath}", filePath);
            throw;
        }
    }

    private string EncryptPassword(string password)
    {
        try
        {
            var data = Encoding.UTF8.GetBytes(password);
            var encrypted = ProtectedData.Protect(data, null, DataProtectionScope.CurrentUser);
            return Convert.ToBase64String(encrypted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to encrypt password");
            return password; // Return original if encryption fails
        }
    }

    private string DecryptPassword(string encryptedPassword)
    {
        try
        {
            var data = Convert.FromBase64String(encryptedPassword);
            var decrypted = ProtectedData.Unprotect(data, null, DataProtectionScope.CurrentUser);
            return Encoding.UTF8.GetString(decrypted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to decrypt password");
            throw;
        }
    }

    private void OnConfigurationChanged(string section, object? oldValue, object? newValue)
    {
        ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs
        {
            Section = section,
            OldValue = oldValue,
            NewValue = newValue
        });
    }
}
