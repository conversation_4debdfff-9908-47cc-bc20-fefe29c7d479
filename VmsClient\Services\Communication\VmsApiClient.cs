using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using VmsClient.Models;

namespace VmsClient.Services.Communication;

/// <summary>
/// HTTP client for communicating with VMS servers
/// </summary>
public class VmsApiClient : IVmsApiClient, IDisposable
{
    private readonly ILogger<VmsApiClient> _logger;
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;
    private VmsServer? _currentServer;
    private string? _authToken;
    private bool _disposed = false;

    public VmsServer? CurrentServer => _currentServer;
    public bool IsConnected => _currentServer?.IsOnline == true && !string.IsNullOrEmpty(_authToken);

    public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

    public VmsApiClient(ILogger<VmsApiClient> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient
        {
            Timeout = TimeSpan.FromSeconds(30)
        };
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
        
        // Set default headers
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _httpClient.DefaultRequestHeaders.UserAgent.Add(new ProductInfoHeaderValue("VmsClient", "1.0"));
    }

    public async Task<bool> ConnectAsync(VmsServer server, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Connecting to VMS server at {Url}", server.Url);
            
            // Set base address
            _httpClient.BaseAddress = new Uri(server.Url);
            
            // Test connection
            var healthResponse = await GetServerHealthAsync(cancellationToken);
            if (healthResponse.IsHealthy)
            {
                _currentServer = server;
                server.IsOnline = true;
                server.LastSeen = DateTime.Now;
                
                OnConnectionStatusChanged(true, server, null);
                _logger.LogInformation("Successfully connected to VMS server at {Url}", server.Url);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to VMS server at {Url}", server.Url);
            OnConnectionStatusChanged(false, server, ex.Message);
            return false;
        }
    }

    public async Task DisconnectAsync()
    {
        if (_currentServer != null)
        {
            _logger.LogInformation("Disconnecting from VMS server at {Url}", _currentServer.Url);
            
            _authToken = null;
            _httpClient.DefaultRequestHeaders.Authorization = null;
            _httpClient.BaseAddress = null;
            
            var server = _currentServer;
            _currentServer = null;
            
            OnConnectionStatusChanged(false, server, null);
        }
    }

    public async Task<bool> AuthenticateAsync(string username, string password, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Authenticating with VMS server");
            
            var loginRequest = new
            {
                Username = username,
                Password = password
            };
            
            var response = await PostAsync<AuthResponse>("/api/auth/login", loginRequest, cancellationToken);
            if (response?.Token != null)
            {
                _authToken = response.Token;
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new AuthenticationHeaderValue("Bearer", _authToken);
                
                _logger.LogInformation("Successfully authenticated with VMS server");
                OnConnectionStatusChanged(true, _currentServer, null);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Authentication failed");
            OnConnectionStatusChanged(false, _currentServer, ex.Message);
            return false;
        }
    }

    public async Task<ServerHealthResponse> GetServerHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Try multiple health endpoints
            var endpoints = new[] { "/api/health", "/health", "/api/status", "/" };
            
            foreach (var endpoint in endpoints)
            {
                try
                {
                    var response = await _httpClient.GetAsync(endpoint, cancellationToken);
                    if (response.IsSuccessStatusCode)
                    {
                        return new ServerHealthResponse
                        {
                            IsHealthy = true,
                            Status = "OK",
                            Timestamp = DateTime.Now,
                            Details = new Dictionary<string, object>
                            {
                                ["endpoint"] = endpoint,
                                ["statusCode"] = (int)response.StatusCode
                            }
                        };
                    }
                }
                catch
                {
                    // Try next endpoint
                    continue;
                }
            }
            
            return new ServerHealthResponse
            {
                IsHealthy = false,
                Status = "Unreachable",
                Timestamp = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return new ServerHealthResponse
            {
                IsHealthy = false,
                Status = "Error",
                Timestamp = DateTime.Now,
                Details = new Dictionary<string, object> { ["error"] = ex.Message }
            };
        }
    }

    public async Task<ServerInfoResponse> GetServerInfoAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await GetAsync<ServerInfoResponse>("/api/info", cancellationToken);
            return response ?? new ServerInfoResponse
            {
                Name = _currentServer?.Name ?? "Unknown VMS Server",
                Version = "Unknown",
                Description = "VMS Server",
                StartTime = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get server info");
            return new ServerInfoResponse
            {
                Name = _currentServer?.Name ?? "Unknown VMS Server",
                Version = "Unknown",
                Description = "VMS Server (Info unavailable)",
                StartTime = DateTime.Now
            };
        }
    }

    public async Task<List<Voucher>> GetVouchersAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await GetAsync<List<Voucher>>("/api/vouchers", cancellationToken);
            return response ?? new List<Voucher>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get vouchers");
            throw;
        }
    }

    public async Task<Voucher> CreateVoucherAsync(CreateVoucherRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await PostAsync<Voucher>("/api/vouchers", request, cancellationToken);
            return response ?? throw new InvalidOperationException("Failed to create voucher");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create voucher");
            throw;
        }
    }

    public async Task<Voucher> UpdateVoucherAsync(string voucherId, UpdateVoucherRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await PutAsync<Voucher>($"/api/vouchers/{voucherId}", request, cancellationToken);
            return response ?? throw new InvalidOperationException("Failed to update voucher");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update voucher {VoucherId}", voucherId);
            throw;
        }
    }

    public async Task<bool> DeleteVoucherAsync(string voucherId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"/api/vouchers/{voucherId}", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete voucher {VoucherId}", voucherId);
            return false;
        }
    }

    public async Task<Voucher?> GetVoucherAsync(string voucherId, CancellationToken cancellationToken = default)
    {
        try
        {
            return await GetAsync<Voucher>($"/api/vouchers/{voucherId}", cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get voucher {VoucherId}", voucherId);
            return null;
        }
    }

    private async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default)
    {
        var response = await _httpClient.GetAsync(endpoint, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<T>(content, _jsonOptions);
    }

    private async Task<T?> PostAsync<T>(string endpoint, object data, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(data, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync(endpoint, content, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);
    }

    private async Task<T?> PutAsync<T>(string endpoint, object data, CancellationToken cancellationToken = default)
    {
        var json = JsonSerializer.Serialize(data, _jsonOptions);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PutAsync(endpoint, content, cancellationToken);
        response.EnsureSuccessStatusCode();
        
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions);
    }

    private void OnConnectionStatusChanged(bool isConnected, VmsServer? server, string? errorMessage)
    {
        ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs
        {
            IsConnected = isConnected,
            Server = server,
            ErrorMessage = errorMessage
        });
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _httpClient?.Dispose();
        _disposed = true;
    }

    private class AuthResponse
    {
        public string? Token { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }
}
