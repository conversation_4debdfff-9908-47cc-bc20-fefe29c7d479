﻿<Window x:Class="VmsClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:VmsClient"
        mc:Ignorable="d"
        Title="{Binding Title}" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="Gray"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🎫" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBlock Text="VMS Client" FontSize="20" FontWeight="Bold"
                              Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Text="Voucher Management System" FontSize="14"
                              Foreground="LightBlue" Margin="15,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border Background="White" CornerRadius="15" Padding="10,5" Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="10" Height="10" Margin="0,0,5,0" VerticalAlignment="Center">
                                <Ellipse.Fill>
                                    <SolidColorBrush Color="{Binding IsConnected, Converter={StaticResource BoolToColorConverter}}"/>
                                </Ellipse.Fill>
                            </Ellipse>
                            <TextBlock Text="{Binding ConnectionStatus}" FontSize="12" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="10">
            <!-- Server Discovery Tab -->
            <TabItem Header="🔍 Server Discovery">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Discovery Controls -->
                    <Border Grid.Row="0" Background="#F5F5F5" Padding="15" CornerRadius="5" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <Button Content="🔍 Start Discovery" Style="{StaticResource ActionButtonStyle}"
                                       Command="{Binding StartDiscoveryCommand}"
                                       IsEnabled="{Binding IsDiscovering, Converter={StaticResource InverseBooleanConverter}}"/>
                                <Button Content="⏹ Stop Discovery" Style="{StaticResource ActionButtonStyle}"
                                       Command="{Binding StopDiscoveryCommand}"
                                       IsEnabled="{Binding IsDiscovering}"/>
                                <Button Content="🔄 Refresh" Style="{StaticResource ActionButtonStyle}"
                                       Command="{Binding RefreshServersCommand}"/>
                            </StackPanel>

                            <TextBlock Grid.Column="1" Text="{Binding DiscoveryStatus}"
                                      Style="{StaticResource StatusTextStyle}" VerticalAlignment="Center"/>
                        </Grid>
                    </Border>

                    <!-- Manual Server Addition -->
                    <Border Grid.Row="1" Background="#E8F5E8" Padding="15" CornerRadius="5" Margin="0,0,0,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="Add Server Manually:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                            <TextBox Grid.Column="1" x:Name="ManualIpTextBox" Margin="0,0,10,0"
                                    VerticalAlignment="Center" Padding="5"/>
                            <TextBlock Grid.Column="2" Text="Port:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBox Grid.Column="3" Text="8080" Margin="0,0,10,0"
                                    VerticalAlignment="Center" Padding="5"/>
                            <Button Grid.Column="4" Content="➕ Add" Style="{StaticResource ActionButtonStyle}"
                                   Command="{Binding AddServerManuallyCommand}"
                                   CommandParameter="{Binding ElementName=ManualIpTextBox, Path=Text}"/>
                        </Grid>
                    </Border>

                    <!-- Discovered Servers List -->
                    <Border Grid.Row="2" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <Border Grid.Row="0" Background="#F0F0F0" Padding="10">
                                <TextBlock Text="{Binding DiscoveredServerCount, StringFormat='Discovered Servers ({0})'}"
                                          Style="{StaticResource HeaderTextStyle}"/>
                            </Border>

                            <DataGrid Grid.Row="1" ItemsSource="{Binding DiscoveredServers}"
                                     SelectedItem="{Binding SelectedServer}"
                                     AutoGenerateColumns="False" CanUserAddRows="False"
                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Status" Width="60">
                                        <DataGridTextColumn.Binding>
                                            <Binding Path="IsOnline">
                                                <Binding.Converter>
                                                    <local:BoolToStatusConverter/>
                                                </Binding.Converter>
                                            </Binding>
                                        </DataGridTextColumn.Binding>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Name" Binding="{Binding DisplayName}" Width="200"/>
                                    <DataGridTextColumn Header="IP Address" Binding="{Binding IpAddress}" Width="120"/>
                                    <DataGridTextColumn Header="Port" Binding="{Binding Port}" Width="60"/>
                                    <DataGridTextColumn Header="Response Time" Binding="{Binding ResponseTimeMs, StringFormat={}{0}ms}" Width="100"/>
                                    <DataGridTextColumn Header="Last Seen" Binding="{Binding LastSeen, StringFormat=HH:mm:ss}" Width="80"/>
                                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="*"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>

                    <!-- Server Actions -->
                    <Border Grid.Row="3" Background="#F5F5F5" Padding="15" CornerRadius="5" Margin="0,10,0,0">
                        <StackPanel Orientation="Horizontal">
                            <Button Content="🔗 Connect" Style="{StaticResource ActionButtonStyle}"
                                   Command="{Binding ConnectToServerCommand}"
                                   IsEnabled="{Binding SelectedServer, Converter={StaticResource NullToBooleanConverter}}"/>
                            <Button Content="❌ Disconnect" Style="{StaticResource ActionButtonStyle}"
                                   Command="{Binding DisconnectFromServerCommand}"
                                   IsEnabled="{Binding IsConnected}"/>
                            <Button Content="💾 Save Server" Style="{StaticResource ActionButtonStyle}"
                                   Command="{Binding SaveCurrentServerCommand}"
                                   IsEnabled="{Binding SelectedServer, Converter={StaticResource NullToBooleanConverter}}"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Voucher Management Tab -->
            <TabItem Header="🎫 Voucher Management">
                <Grid Margin="10">
                    <TextBlock Text="Voucher Management functionality will be implemented here"
                              HorizontalAlignment="Center" VerticalAlignment="Center"
                              FontSize="16" Foreground="Gray"/>
                </Grid>
            </TabItem>

            <!-- Settings Tab -->
            <TabItem Header="⚙️ Settings">
                <Grid Margin="10">
                    <TextBlock Text="Settings and configuration options will be implemented here"
                              HorizontalAlignment="Center" VerticalAlignment="Center"
                              FontSize="16" Foreground="Gray"/>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" BorderBrush="#DDD" BorderThickness="0,1,0,0" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding DiscoveryStatus}" Style="{StaticResource StatusTextStyle}"/>
                <TextBlock Grid.Column="1" Text="VMS Client v1.0"
                          Style="{StaticResource StatusTextStyle}"/>
            </Grid>
        </Border>
    </Grid>
</Window>
