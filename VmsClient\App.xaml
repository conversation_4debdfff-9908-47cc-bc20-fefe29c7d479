﻿<Application x:Class="VmsClient.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:VmsClient">
    <Application.Resources>
        <!-- Value Converters -->
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
        <local:NullToBooleanConverter x:Key="NullToBooleanConverter"/>
        <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <local:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
    </Application.Resources>
</Application>
