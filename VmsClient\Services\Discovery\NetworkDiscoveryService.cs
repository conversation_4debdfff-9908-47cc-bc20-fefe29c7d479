using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using VmsClient.Models;

namespace VmsClient.Services.Discovery;

/// <summary>
/// Network discovery service that can find VMS servers using multiple discovery methods
/// </summary>
public class NetworkDiscoveryService : INetworkDiscoveryService, IDisposable
{
    private readonly ILogger<NetworkDiscoveryService> _logger;
    private readonly HttpClient _httpClient;
    private readonly ConcurrentDictionary<string, VmsServer> _discoveredServers = new();
    private readonly Timer? _discoveryTimer;
    private readonly SemaphoreSlim _discoveryLock = new(1, 1);
    private bool _isDiscovering = false;
    private bool _disposed = false;

    public event EventHandler<VmsServer>? ServerDiscovered;
    public event EventHandler<VmsServer>? ServerLost;
    public event EventHandler<DiscoveryProgressEventArgs>? DiscoveryProgress;

    public IReadOnlyList<VmsServer> DiscoveredServers => _discoveredServers.Values.ToList();
    public bool IsDiscovering => _isDiscovering;

    public NetworkDiscoveryService(ILogger<NetworkDiscoveryService> logger)
    {
        _logger = logger;
        _httpClient = new HttpClient { Timeout = TimeSpan.FromSeconds(5) };
        
        // Set up periodic discovery every 30 seconds
        _discoveryTimer = new Timer(async _ => await PeriodicDiscoveryAsync(), null, 
            Timeout.InfiniteTimeSpan, TimeSpan.FromSeconds(30));
    }

    public async Task StartDiscoveryAsync(CancellationToken cancellationToken = default)
    {
        await _discoveryLock.WaitAsync(cancellationToken);
        try
        {
            if (_isDiscovering) return;
            
            _isDiscovering = true;
            _logger.LogInformation("Starting network discovery service");
            
            // Start periodic discovery
            _discoveryTimer?.Change(TimeSpan.Zero, TimeSpan.FromSeconds(30));
            
            // Perform initial discovery
            await DiscoverServersAsync(cancellationToken);
        }
        finally
        {
            _discoveryLock.Release();
        }
    }

    public async Task StopDiscoveryAsync()
    {
        await _discoveryLock.WaitAsync();
        try
        {
            if (!_isDiscovering) return;
            
            _isDiscovering = false;
            _logger.LogInformation("Stopping network discovery service");
            
            // Stop periodic discovery
            _discoveryTimer?.Change(Timeout.InfiniteTimeSpan, Timeout.InfiniteTimeSpan);
        }
        finally
        {
            _discoveryLock.Release();
        }
    }

    public async Task<IList<VmsServer>> DiscoverServersAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting server discovery scan");
        
        var discoveredServers = new List<VmsServer>();
        var networkInterfaces = GetActiveNetworkInterfaces();
        
        var totalHosts = networkInterfaces.Sum(ni => GetHostCountForNetwork(ni));
        var scannedHosts = 0;
        
        ReportProgress(totalHosts, scannedHosts, discoveredServers.Count, "Starting network scan...");

        foreach (var networkInterface in networkInterfaces)
        {
            if (cancellationToken.IsCancellationRequested) break;
            
            var servers = await ScanNetworkAsync(networkInterface, 
                (scanned, found, operation) => 
                {
                    scannedHosts += scanned;
                    ReportProgress(totalHosts, scannedHosts, found, operation);
                }, 
                cancellationToken);
                
            discoveredServers.AddRange(servers);
        }

        // Update discovered servers collection
        foreach (var server in discoveredServers)
        {
            var key = $"{server.IpAddress}:{server.Port}";
            if (_discoveredServers.TryAdd(key, server))
            {
                ServerDiscovered?.Invoke(this, server);
            }
            else
            {
                // Update existing server
                _discoveredServers[key] = server;
            }
        }

        _logger.LogInformation("Discovery scan completed. Found {Count} servers", discoveredServers.Count);
        return discoveredServers;
    }

    private async Task<List<VmsServer>> ScanNetworkAsync(NetworkInterface networkInterface, 
        Action<int, int, string> progressCallback, CancellationToken cancellationToken)
    {
        var servers = new List<VmsServer>();
        var ipProperties = networkInterface.GetIPProperties();
        
        foreach (var unicastAddress in ipProperties.UnicastAddresses)
        {
            if (unicastAddress.Address.AddressFamily != AddressFamily.InterNetwork) continue;
            if (IPAddress.IsLoopback(unicastAddress.Address)) continue;
            
            var networkAddress = GetNetworkAddress(unicastAddress.Address, unicastAddress.IPv4Mask);
            var hostAddresses = GetHostAddresses(networkAddress, unicastAddress.IPv4Mask);
            
            var tasks = hostAddresses.Select(async (ip, index) =>
            {
                if (cancellationToken.IsCancellationRequested) return null;
                
                progressCallback(1, servers.Count, $"Scanning {ip}...");
                
                var server = await TestVmsServerAsync(ip, 8080, cancellationToken);
                if (server != null)
                {
                    servers.Add(server);
                    _logger.LogInformation("Found VMS server at {Url}", server.Url);
                }
                
                return server;
            });
            
            await Task.WhenAll(tasks);
        }
        
        return servers;
    }
    
    private async Task<VmsServer?> TestVmsServerAsync(IPAddress ipAddress, int port, CancellationToken cancellationToken)
    {
        try
        {
            // First, test if port is open
            using var tcpClient = new TcpClient();
            var connectTask = tcpClient.ConnectAsync(ipAddress, port);
            var timeoutTask = Task.Delay(2000, cancellationToken);
            
            if (await Task.WhenAny(connectTask, timeoutTask) != connectTask)
            {
                return null; // Timeout or cancelled
            }
            
            if (!tcpClient.Connected) return null;
            
            // Test HTTP endpoint
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var url = $"http://{ipAddress}:{port}";
            
            try
            {
                var response = await _httpClient.GetAsync($"{url}/api/health", cancellationToken);
                stopwatch.Stop();
                
                var server = new VmsServer
                {
                    IpAddress = ipAddress,
                    Port = port,
                    IsOnline = true,
                    ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds,
                    LastSeen = DateTime.Now
                };
                
                // Try to get server info
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync(cancellationToken);
                    // Parse server info if available
                    server.Name = $"VMS Server ({ipAddress})";
                    server.Description = "Voucher Management System";
                }
                
                return server;
            }
            catch (HttpRequestException)
            {
                // Port is open but not HTTP, might still be VMS server
                stopwatch.Stop();
                return new VmsServer
                {
                    IpAddress = ipAddress,
                    Port = port,
                    IsOnline = true,
                    ResponseTimeMs = (int)stopwatch.ElapsedMilliseconds,
                    LastSeen = DateTime.Now,
                    Name = $"VMS Server ({ipAddress})",
                    Description = "Potential VMS Server (Port Open)"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug("Failed to connect to {IpAddress}:{Port} - {Message}", ipAddress, port, ex.Message);
            return null;
        }
    }

    public async Task<bool> TestServerConnectionAsync(VmsServer server, CancellationToken cancellationToken = default)
    {
        var testResult = await TestVmsServerAsync(server.IpAddress, server.Port, cancellationToken);
        if (testResult != null)
        {
            server.IsOnline = true;
            server.ResponseTimeMs = testResult.ResponseTimeMs;
            server.LastSeen = DateTime.Now;
            return true;
        }
        
        server.IsOnline = false;
        return false;
    }

    public async Task AddServerManuallyAsync(string ipAddress, int port = 8080)
    {
        if (!IPAddress.TryParse(ipAddress, out var ip))
        {
            throw new ArgumentException("Invalid IP address format", nameof(ipAddress));
        }
        
        var server = new VmsServer
        {
            IpAddress = ip,
            Port = port,
            Name = $"Manual Server ({ip})",
            Description = "Manually added server"
        };
        
        // Test the connection
        await TestServerConnectionAsync(server);
        
        var key = $"{server.IpAddress}:{server.Port}";
        if (_discoveredServers.TryAdd(key, server))
        {
            ServerDiscovered?.Invoke(this, server);
            _logger.LogInformation("Manually added server {Url}", server.Url);
        }
    }

    private async Task PeriodicDiscoveryAsync()
    {
        if (!_isDiscovering) return;
        
        try
        {
            await DiscoverServersAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during periodic discovery");
        }
    }

    private void ReportProgress(int totalHosts, int scannedHosts, int foundServers, string operation)
    {
        DiscoveryProgress?.Invoke(this, new DiscoveryProgressEventArgs
        {
            TotalHosts = totalHosts,
            ScannedHosts = scannedHosts,
            FoundServers = foundServers,
            CurrentOperation = operation
        });
    }

    private static List<NetworkInterface> GetActiveNetworkInterfaces()
    {
        return NetworkInterface.GetAllNetworkInterfaces()
            .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel)
            .ToList();
    }

    private static int GetHostCountForNetwork(NetworkInterface networkInterface)
    {
        var ipProperties = networkInterface.GetIPProperties();
        return ipProperties.UnicastAddresses
            .Where(ua => ua.Address.AddressFamily == AddressFamily.InterNetwork &&
                        !IPAddress.IsLoopback(ua.Address))
            .Sum(ua => GetHostCount(ua.IPv4Mask));
    }

    private static int GetHostCount(IPAddress subnetMask)
    {
        var maskBytes = subnetMask.GetAddressBytes();
        var hostBits = 0;
        
        for (int i = 3; i >= 0; i--)
        {
            var octet = maskBytes[i];
            for (int bit = 0; bit < 8; bit++)
            {
                if ((octet & (1 << bit)) == 0)
                    hostBits++;
                else
                    break;
            }
            if (octet != 0) break;
        }
        
        return Math.Min((int)Math.Pow(2, hostBits) - 2, 254); // Limit to reasonable scan size
    }

    private static IPAddress GetNetworkAddress(IPAddress address, IPAddress subnetMask)
    {
        var addressBytes = address.GetAddressBytes();
        var maskBytes = subnetMask.GetAddressBytes();
        var networkBytes = new byte[4];
        
        for (int i = 0; i < 4; i++)
        {
            networkBytes[i] = (byte)(addressBytes[i] & maskBytes[i]);
        }
        
        return new IPAddress(networkBytes);
    }

    private static List<IPAddress> GetHostAddresses(IPAddress networkAddress, IPAddress subnetMask)
    {
        var addresses = new List<IPAddress>();
        var networkBytes = networkAddress.GetAddressBytes();
        var maskBytes = subnetMask.GetAddressBytes();
        
        // For simplicity, scan common ranges
        if (networkBytes[0] == 10 || (networkBytes[0] == 172 && networkBytes[1] >= 16 && networkBytes[1] <= 31) ||
            (networkBytes[0] == 192 && networkBytes[1] == 168))
        {
            // Scan the last octet
            for (int i = 1; i < 255; i++)
            {
                var hostBytes = (byte[])networkBytes.Clone();
                hostBytes[3] = (byte)i;
                addresses.Add(new IPAddress(hostBytes));
            }
        }
        
        return addresses;
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _discoveryTimer?.Dispose();
        _httpClient?.Dispose();
        _discoveryLock?.Dispose();
        _disposed = true;
    }
}
