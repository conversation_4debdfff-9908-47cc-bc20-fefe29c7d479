using System;
using System.Threading.Tasks;

namespace VmsClient;

/// <summary>
/// Simple test program to verify VMS connectivity
/// </summary>
public class TestProgram
{
    public static async Task Main(string[] args)
    {
        if (args.Length > 0 && args[0] == "--test")
        {
            await TestVmsConnection.RunAllTestsAsync();
            return;
        }
        
        // Normal application startup
        var app = new App();
        app.Run();
    }
}
