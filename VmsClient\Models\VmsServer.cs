using System.Net;

namespace VmsClient.Models;

/// <summary>
/// Represents a discovered VMS server on the network
/// </summary>
public class VmsServer
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = string.Empty;
    public IPAddress IpAddress { get; set; } = IPAddress.None;
    public int Port { get; set; } = 8080;
    public string Version { get; set; } = string.Empty;
    public DateTime LastSeen { get; set; } = DateTime.Now;
    public bool IsOnline { get; set; } = false;
    public int ResponseTimeMs { get; set; } = 0;
    public string Description { get; set; } = string.Empty;
    public VmsServerCapabilities Capabilities { get; set; } = new();
    
    /// <summary>
    /// Full URL to the VMS server
    /// </summary>
    public string Url => $"http://{IpAddress}:{Port}";
    
    /// <summary>
    /// Display name for the server
    /// </summary>
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : $"{IpAddress}:{Port}";
    
    public override string ToString() => DisplayName;
    
    public override bool Equals(object? obj)
    {
        if (obj is VmsServer other)
        {
            return IpAddress.Equals(other.IpAddress) && Port == other.Port;
        }
        return false;
    }
    
    public override int GetHashCode() => HashCode.Combine(IpAddress, Port);
}

/// <summary>
/// Represents the capabilities of a VMS server
/// </summary>
public class VmsServerCapabilities
{
    public bool SupportsVoucherManagement { get; set; } = true;
    public bool SupportsReporting { get; set; } = false;
    public bool SupportsUserManagement { get; set; } = false;
    public bool RequiresAuthentication { get; set; } = true;
    public List<string> SupportedVersions { get; set; } = new();
}
