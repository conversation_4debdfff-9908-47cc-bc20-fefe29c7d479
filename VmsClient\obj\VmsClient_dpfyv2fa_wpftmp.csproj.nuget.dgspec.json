{"format": 1, "restore": {"C:\\THE PAYROLL AUDITOR\\VmsClient\\VmsClient.csproj": {}}, "projects": {"C:\\THE PAYROLL AUDITOR\\VmsClient\\VmsClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\THE PAYROLL AUDITOR\\VmsClient\\VmsClient.csproj", "projectName": "VmsClient", "projectPath": "C:\\THE PAYROLL AUDITOR\\VmsClient\\VmsClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\THE PAYROLL AUDITOR\\VmsClient\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.8, )"}, "System.Net.NetworkInformation": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}}}}