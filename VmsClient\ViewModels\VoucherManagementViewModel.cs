using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using VmsClient.Services.Communication;

namespace VmsClient.ViewModels;

/// <summary>
/// Voucher management view model
/// </summary>
public partial class VoucherManagementViewModel : ObservableObject
{
    private readonly ILogger<VoucherManagementViewModel> _logger;
    private readonly IVmsApiClient _apiClient;

    [ObservableProperty]
    private bool _isConnected = false;

    [ObservableProperty]
    private bool _isLoading = false;

    [ObservableProperty]
    private string _statusMessage = "Not connected to server";

    [ObservableProperty]
    private Voucher? _selectedVoucher;

    [ObservableProperty]
    private decimal _newVoucherAmount = 0;

    [ObservableProperty]
    private string _newVoucherCurrency = "USD";

    [ObservableProperty]
    private string _newVoucherDescription = string.Empty;

    [ObservableProperty]
    private DateTime? _newVoucherExpiresAt;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private VoucherStatus _filterStatus = VoucherStatus.Active;

    [ObservableProperty]
    private bool _showAllStatuses = true;

    public ObservableCollection<Voucher> Vouchers { get; } = new();
    public ObservableCollection<Voucher> FilteredVouchers { get; } = new();

    public VoucherManagementViewModel(
        ILogger<VoucherManagementViewModel> logger,
        IVmsApiClient apiClient)
    {
        _logger = logger;
        _apiClient = apiClient;

        // Subscribe to connection events
        _apiClient.ConnectionStatusChanged += OnConnectionStatusChanged;

        // Set up property change handlers
        PropertyChanged += OnPropertyChanged;
    }

    [RelayCommand]
    private async Task LoadVouchersAsync()
    {
        if (!IsConnected)
        {
            StatusMessage = "Not connected to server";
            return;
        }

        try
        {
            IsLoading = true;
            StatusMessage = "Loading vouchers...";

            var vouchers = await _apiClient.GetVouchersAsync();
            
            Vouchers.Clear();
            foreach (var voucher in vouchers)
            {
                Vouchers.Add(voucher);
            }

            ApplyFilters();
            StatusMessage = $"Loaded {vouchers.Count} vouchers";
            
            _logger.LogInformation("Loaded {Count} vouchers", vouchers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load vouchers");
            StatusMessage = $"Failed to load vouchers: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task CreateVoucherAsync()
    {
        if (!IsConnected)
        {
            StatusMessage = "Not connected to server";
            return;
        }

        if (NewVoucherAmount <= 0)
        {
            StatusMessage = "Please enter a valid amount";
            return;
        }

        try
        {
            IsLoading = true;
            StatusMessage = "Creating voucher...";

            var request = new CreateVoucherRequest
            {
                Amount = NewVoucherAmount,
                Currency = NewVoucherCurrency,
                Description = NewVoucherDescription,
                ExpiresAt = NewVoucherExpiresAt
            };

            var voucher = await _apiClient.CreateVoucherAsync(request);
            Vouchers.Insert(0, voucher);
            
            // Clear form
            NewVoucherAmount = 0;
            NewVoucherDescription = string.Empty;
            NewVoucherExpiresAt = null;

            ApplyFilters();
            StatusMessage = $"Created voucher {voucher.Code}";
            
            _logger.LogInformation("Created voucher {VoucherCode}", voucher.Code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create voucher");
            StatusMessage = $"Failed to create voucher: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task UpdateVoucherAsync(Voucher? voucher)
    {
        if (!IsConnected || voucher == null)
        {
            StatusMessage = "Not connected to server or no voucher selected";
            return;
        }

        try
        {
            IsLoading = true;
            StatusMessage = $"Updating voucher {voucher.Code}...";

            var request = new UpdateVoucherRequest
            {
                Status = voucher.Status,
                Description = voucher.Description,
                ExpiresAt = voucher.ExpiresAt
            };

            var updatedVoucher = await _apiClient.UpdateVoucherAsync(voucher.Id, request);
            
            // Update in collection
            var index = Vouchers.IndexOf(voucher);
            if (index >= 0)
            {
                Vouchers[index] = updatedVoucher;
            }

            ApplyFilters();
            StatusMessage = $"Updated voucher {voucher.Code}";
            
            _logger.LogInformation("Updated voucher {VoucherCode}", voucher.Code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update voucher");
            StatusMessage = $"Failed to update voucher: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task DeleteVoucherAsync(Voucher? voucher)
    {
        if (!IsConnected || voucher == null)
        {
            StatusMessage = "Not connected to server or no voucher selected";
            return;
        }

        try
        {
            IsLoading = true;
            StatusMessage = $"Deleting voucher {voucher.Code}...";

            var success = await _apiClient.DeleteVoucherAsync(voucher.Id);
            if (success)
            {
                Vouchers.Remove(voucher);
                ApplyFilters();
                StatusMessage = $"Deleted voucher {voucher.Code}";
                
                _logger.LogInformation("Deleted voucher {VoucherCode}", voucher.Code);
            }
            else
            {
                StatusMessage = $"Failed to delete voucher {voucher.Code}";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete voucher");
            StatusMessage = $"Failed to delete voucher: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task RefreshVouchersAsync()
    {
        await LoadVouchersAsync();
    }

    [RelayCommand]
    private void ClearFilters()
    {
        SearchText = string.Empty;
        ShowAllStatuses = true;
        ApplyFilters();
    }

    [RelayCommand]
    private void ApplyFilters()
    {
        try
        {
            FilteredVouchers.Clear();

            var filtered = Vouchers.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                filtered = filtered.Where(v => 
                    v.Code.ToLower().Contains(searchLower) ||
                    v.Description.ToLower().Contains(searchLower) ||
                    v.CreatedBy.ToLower().Contains(searchLower));
            }

            // Apply status filter
            if (!ShowAllStatuses)
            {
                filtered = filtered.Where(v => v.Status == FilterStatus);
            }

            // Sort by creation date (newest first)
            filtered = filtered.OrderByDescending(v => v.CreatedAt);

            foreach (var voucher in filtered)
            {
                FilteredVouchers.Add(voucher);
            }

            _logger.LogDebug("Applied filters, showing {Count} of {Total} vouchers", 
                FilteredVouchers.Count, Vouchers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply filters");
        }
    }

    private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsConnected = e.IsConnected;
            
            if (e.IsConnected)
            {
                StatusMessage = $"Connected to {e.Server?.DisplayName}";
                // Auto-load vouchers when connected
                _ = Task.Run(LoadVouchersAsync);
            }
            else
            {
                StatusMessage = "Disconnected from server";
                Vouchers.Clear();
                FilteredVouchers.Clear();
            }
        });
    }

    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SearchText) || 
            e.PropertyName == nameof(FilterStatus) || 
            e.PropertyName == nameof(ShowAllStatuses))
        {
            ApplyFilters();
        }
    }
}
